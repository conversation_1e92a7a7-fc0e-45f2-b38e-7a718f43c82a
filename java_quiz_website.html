<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>☕ Java MCQ Quiz</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .quiz-setup {
            text-align: center;
        }

        .quiz-setup h2 {
            color: #333;
            margin-bottom: 20px;
        }

        .quiz-setup select {
            padding: 12px 20px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px;
            background: white;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .question-container {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .question-number {
            color: #667eea;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .question-text {
            font-size: 1.1rem;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .options {
            list-style: none;
        }

        .option {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s;
        }

        .option:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .option.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }

        .progress-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(135deg, #667eea, #764ba2);
            height: 100%;
            transition: width 0.3s;
        }

        .results {
            text-align: center;
            padding: 30px;
        }

        .score {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }

        .performance-message {
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .hidden {
            display: none;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            color: #667eea;
            font-weight: bold;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 600px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>☕ Java MCQ Quiz</h1>
            <p>Test your knowledge of Java programming concepts</p>
        </div>

        <div class="content">
            <!-- Quiz Setup -->
            <div id="quiz-setup" class="quiz-setup">
                <h2>🚀 Ready to test your Java knowledge?</h2>
                <p>Select the number of questions you'd like to attempt and start the quiz!</p>
                <br>
                <select id="num-questions">
                    <option value="5">5 questions</option>
                    <option value="10">10 questions</option>
                    <option value="15" selected>15 questions</option>
                    <option value="20">20 questions</option>
                    <option value="25">25 questions</option>
                    <option value="30">30 questions</option>
                    <option value="50">50 questions</option>
                    <option value="110">All 110 questions</option>
                </select>
                <br>
                <button class="btn" onclick="startQuiz()">Start Quiz</button>
            </div>

            <!-- Quiz Questions -->
            <div id="quiz-container" class="hidden">
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
                </div>
                
                <div class="stats">
                    <div class="stat">
                        <div id="current-question" class="stat-number">1</div>
                        <div class="stat-label">Current Question</div>
                    </div>
                    <div class="stat">
                        <div id="total-questions" class="stat-number">15</div>
                        <div class="stat-label">Total Questions</div>
                    </div>
                    <div class="stat">
                        <div id="current-score" class="stat-number">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                </div>

                <div id="question-container" class="question-container">
                    <div id="question-number" class="question-number">Question 1 of 15</div>
                    <div id="question-text" class="question-text"></div>
                    <ul id="options" class="options"></ul>
                </div>

                <div style="text-align: center;">
                    <button id="submit-btn" class="btn" onclick="submitAnswer()" disabled>Submit Answer</button>
                </div>
            </div>

            <!-- Results -->
            <div id="results-container" class="hidden results">
                <h2>🎉 Quiz Completed!</h2>
                <div id="final-score" class="score">0/15</div>
                <div id="percentage" class="score" style="font-size: 2rem;">0%</div>
                <div id="performance-message" class="performance-message"></div>
                
                <div class="stats">
                    <div class="stat">
                        <div id="correct-answers" class="stat-number">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat">
                        <div id="incorrect-answers" class="stat-number">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat">
                        <div id="final-percentage" class="stat-number">0%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                </div>

                <button class="btn" onclick="restartQuiz()">Start New Quiz</button>
            </div>
        </div>
    </div>

    <script src="quiz_data.js"></script>
    <script>
        // Use the questions from the external file
        const quizQuestions = allQuizQuestions;

        let currentQuiz = [];
        let currentQuestionIndex = 0;
        let score = 0;
        let selectedAnswer = null;

        function startQuiz() {
            const numQuestions = parseInt(document.getElementById('num-questions').value);
            
            // Select random questions
            currentQuiz = getRandomQuestions(numQuestions);
            currentQuestionIndex = 0;
            score = 0;
            selectedAnswer = null;

            // Update UI
            document.getElementById('quiz-setup').classList.add('hidden');
            document.getElementById('quiz-container').classList.remove('hidden');
            document.getElementById('total-questions').textContent = currentQuiz.length;
            
            displayQuestion();
        }

        function getRandomQuestions(num) {
            const shuffled = [...quizQuestions].sort(() => 0.5 - Math.random());
            return shuffled.slice(0, Math.min(num, quizQuestions.length));
        }

        function displayQuestion() {
            const question = currentQuiz[currentQuestionIndex];
            const progress = ((currentQuestionIndex + 1) / currentQuiz.length) * 100;
            
            document.getElementById('progress-fill').style.width = progress + '%';
            document.getElementById('current-question').textContent = currentQuestionIndex + 1;
            document.getElementById('question-number').textContent = `Question ${currentQuestionIndex + 1} of ${currentQuiz.length}`;
            document.getElementById('question-text').textContent = question.question_text;
            
            const optionsContainer = document.getElementById('options');
            optionsContainer.innerHTML = '';
            
            question.options.forEach((option, index) => {
                const li = document.createElement('li');
                li.className = 'option';
                li.textContent = option;
                li.onclick = () => selectOption(li, String.fromCharCode(97 + index)); // a, b, c, d
                optionsContainer.appendChild(li);
            });
            
            document.getElementById('submit-btn').disabled = true;
            selectedAnswer = null;
        }

        function selectOption(element, answer) {
            // Remove previous selection
            document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            
            // Select current option
            element.classList.add('selected');
            selectedAnswer = answer;
            document.getElementById('submit-btn').disabled = false;
        }

        function submitAnswer() {
            if (!selectedAnswer) return;
            
            const question = currentQuiz[currentQuestionIndex];
            const isCorrect = selectedAnswer === question.correct_answer;
            
            if (isCorrect) {
                score++;
                document.getElementById('current-score').textContent = score;
            }
            
            currentQuestionIndex++;
            
            if (currentQuestionIndex < currentQuiz.length) {
                setTimeout(displayQuestion, 500);
            } else {
                setTimeout(showResults, 500);
            }
        }

        function showResults() {
            const totalQuestions = currentQuiz.length;
            const percentage = Math.round((score / totalQuestions) * 100);
            
            document.getElementById('quiz-container').classList.add('hidden');
            document.getElementById('results-container').classList.remove('hidden');
            
            document.getElementById('final-score').textContent = `${score}/${totalQuestions}`;
            document.getElementById('percentage').textContent = `${percentage}%`;
            document.getElementById('correct-answers').textContent = score;
            document.getElementById('incorrect-answers').textContent = totalQuestions - score;
            document.getElementById('final-percentage').textContent = `${percentage}%`;
            
            let message = '';
            if (percentage >= 80) {
                message = '🌟 Excellent! You have a great understanding of Java!';
            } else if (percentage >= 60) {
                message = '👍 Good job! Keep practicing to improve further!';
            } else if (percentage >= 40) {
                message = '📚 Not bad! Consider reviewing the concepts and try again!';
            } else {
                message = '💪 Keep studying! Practice makes perfect!';
            }
            
            document.getElementById('performance-message').textContent = message;
        }

        function restartQuiz() {
            document.getElementById('results-container').classList.add('hidden');
            document.getElementById('quiz-setup').classList.remove('hidden');
        }
    </script>
</body>
</html>
