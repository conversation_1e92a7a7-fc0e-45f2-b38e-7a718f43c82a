import streamlit as st
import random
from quiz_questions import quiz_questions

# Configure the page
st.set_page_config(
    page_title="Java MCQ Quiz",
    page_icon="☕",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #FF6B35;
        text-align: center;
        margin-bottom: 2rem;
    }
    .question-container {
        background-color: #f0f2f6;
        padding: 1.5rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
    .option-button {
        width: 100%;
        margin: 0.5rem 0;
    }
    .score-container {
        background-color: #e8f4fd;
        padding: 1.5rem;
        border-radius: 10px;
        text-align: center;
    }
    .correct-answer {
        color: #28a745;
        font-weight: bold;
    }
    .incorrect-answer {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'quiz_started' not in st.session_state:
        st.session_state.quiz_started = False
    if 'current_question' not in st.session_state:
        st.session_state.current_question = 0
    if 'score' not in st.session_state:
        st.session_state.score = 0
    if 'selected_questions' not in st.session_state:
        st.session_state.selected_questions = []
    if 'user_answers' not in st.session_state:
        st.session_state.user_answers = []
    if 'quiz_completed' not in st.session_state:
        st.session_state.quiz_completed = False
    if 'show_results' not in st.session_state:
        st.session_state.show_results = False

def start_new_quiz(num_questions):
    """Start a new quiz with specified number of questions"""
    st.session_state.quiz_started = True
    st.session_state.current_question = 0
    st.session_state.score = 0
    st.session_state.quiz_completed = False
    st.session_state.show_results = False
    st.session_state.user_answers = []
    
    if num_questions > len(quiz_questions):
        st.session_state.selected_questions = quiz_questions.copy()
    else:
        st.session_state.selected_questions = random.sample(quiz_questions, num_questions)

def check_answer(user_answer, correct_answer):
    """Check if the user's answer is correct"""
    return user_answer.lower() == correct_answer.lower()

def display_question():
    """Display the current question and options"""
    if st.session_state.current_question < len(st.session_state.selected_questions):
        question = st.session_state.selected_questions[st.session_state.current_question]
        
        # Progress bar
        progress = (st.session_state.current_question + 1) / len(st.session_state.selected_questions)
        st.progress(progress)
        
        # Question header
        st.markdown(f"### Question {st.session_state.current_question + 1} of {len(st.session_state.selected_questions)}")
        
        # Question text
        with st.container():
            st.markdown('<div class="question-container">', unsafe_allow_html=True)
            st.markdown(f"**{question['question_text']}**")
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Options
        options = question['options']
        option_labels = ['A', 'B', 'C', 'D']
        
        # Create radio buttons for options
        selected_option = st.radio(
            "Choose your answer:",
            options,
            key=f"question_{st.session_state.current_question}",
            format_func=lambda x: x
        )
        
        # Submit button
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button("Submit Answer", key="submit_btn", use_container_width=True):
                # Extract the option letter (a, b, c, d)
                user_answer = option_labels[options.index(selected_option)].lower()
                
                # Store the answer
                st.session_state.user_answers.append({
                    'question': question['question_text'],
                    'user_answer': user_answer,
                    'correct_answer': question['correct_answer'],
                    'options': options,
                    'is_correct': check_answer(user_answer, question['correct_answer'])
                })
                
                # Update score
                if check_answer(user_answer, question['correct_answer']):
                    st.session_state.score += 1
                
                # Move to next question
                st.session_state.current_question += 1
                
                # Check if quiz is completed
                if st.session_state.current_question >= len(st.session_state.selected_questions):
                    st.session_state.quiz_completed = True
                
                st.rerun()

def display_results():
    """Display quiz results"""
    total_questions = len(st.session_state.selected_questions)
    score = st.session_state.score
    percentage = (score / total_questions) * 100 if total_questions > 0 else 0
    
    # Results header
    st.markdown('<div class="score-container">', unsafe_allow_html=True)
    st.markdown("# 🎉 Quiz Completed!")
    st.markdown(f"## Your Score: {score}/{total_questions}")
    st.markdown(f"## Percentage: {percentage:.1f}%")
    
    # Performance message
    if percentage >= 80:
        st.markdown("### 🌟 Excellent! You have a great understanding of Java!")
    elif percentage >= 60:
        st.markdown("### 👍 Good job! Keep practicing to improve further!")
    elif percentage >= 40:
        st.markdown("### 📚 Not bad! Consider reviewing the concepts and try again!")
    else:
        st.markdown("### 💪 Keep studying! Practice makes perfect!")
    
    st.markdown('</div>', unsafe_allow_html=True)
    
    # Show detailed results button
    if st.button("Show Detailed Results", use_container_width=True):
        st.session_state.show_results = True
        st.rerun()
    
    # Start new quiz button
    if st.button("Start New Quiz", use_container_width=True):
        st.session_state.quiz_started = False
        st.session_state.show_results = False
        st.rerun()

def display_detailed_results():
    """Display detailed results for each question"""
    st.markdown("## 📊 Detailed Results")
    
    for i, answer in enumerate(st.session_state.user_answers):
        with st.expander(f"Question {i+1}: {'✅ Correct' if answer['is_correct'] else '❌ Incorrect'}"):
            st.markdown(f"**Question:** {answer['question']}")
            
            # Show options with highlighting
            for j, option in enumerate(answer['options']):
                option_letter = ['a', 'b', 'c', 'd'][j]
                if option_letter == answer['correct_answer']:
                    st.markdown(f"✅ **{option}** (Correct Answer)")
                elif option_letter == answer['user_answer'] and not answer['is_correct']:
                    st.markdown(f"❌ **{option}** (Your Answer)")
                else:
                    st.markdown(f"   {option}")
    
    if st.button("Back to Results", use_container_width=True):
        st.session_state.show_results = False
        st.rerun()

def main():
    """Main application function"""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">☕ Java MCQ Quiz</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar
    with st.sidebar:
        st.markdown("## 📋 Quiz Information")
        st.markdown(f"**Total Questions Available:** {len(quiz_questions)}")
        st.markdown("**Topics Covered:**")
        st.markdown("- Data Types & Variables")
        st.markdown("- OOP Concepts")
        st.markdown("- Inheritance & Polymorphism")
        st.markdown("- String Handling")
        st.markdown("- Exception Handling")
        st.markdown("- Threading")
        st.markdown("- And much more!")
        
        if st.session_state.quiz_started and not st.session_state.quiz_completed:
            st.markdown("---")
            st.markdown("## 📈 Current Progress")
            st.markdown(f"**Question:** {st.session_state.current_question + 1}/{len(st.session_state.selected_questions)}")
            st.markdown(f"**Score:** {st.session_state.score}")
    
    # Main content
    if not st.session_state.quiz_started:
        # Quiz setup
        st.markdown("## 🚀 Ready to test your Java knowledge?")
        st.markdown("Select the number of questions you'd like to attempt and start the quiz!")
        
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            num_questions = st.selectbox(
                "Number of questions:",
                options=[5, 10, 15, 20, 25, 30, 50, len(quiz_questions)],
                index=2,
                format_func=lambda x: f"{x} questions" if x != len(quiz_questions) else f"All {x} questions"
            )
            
            if st.button("Start Quiz", use_container_width=True, type="primary"):
                start_new_quiz(num_questions)
                st.rerun()
    
    elif st.session_state.quiz_completed:
        if st.session_state.show_results:
            display_detailed_results()
        else:
            display_results()
    
    else:
        # Display current question
        display_question()

if __name__ == "__main__":
    main()
